'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { formatUSDT } from '@/lib/utils'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  createdAt: string
  product: {
    id: string
    title: string
    price: number
    images: any
  }
  buyer: {
    id: string
    name: string | null
    email: string | null
  }
  seller: {
    id: string
    name: string | null
    email: string | null
  }
}

interface OrdersResponse {
  orders: Order[]
  pagination: {
    page: number
    limit: number
    total: number
    pages: number
  }
}

export default function OrdersPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [orders, setOrders] = useState<Order[]>([])
  const [pagination, setPagination] = useState({
    page: 1,
    limit: 20,
    total: 0,
    pages: 0
  })
  const [filters, setFilters] = useState({
    type: 'all', // 'all', 'buyer', 'seller'
    status: '',
    search: ''
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    fetchOrders()
  }, [session, pagination.page, filters])

  const fetchOrders = async () => {
    setIsLoading(true)
    try {
      const params = new URLSearchParams({
        page: pagination.page.toString(),
        limit: pagination.limit.toString(),
        ...(filters.type !== 'all' && { type: filters.type }),
        ...(filters.status && { status: filters.status }),
        ...(filters.search && { search: filters.search })
      })

      const response = await fetch(`/api/orders?${params}`)
      if (response.ok) {
        const data: OrdersResponse = await response.json()
        setOrders(data.orders)
        setPagination(data.pagination)
      }
    } catch (error) {
      console.error('Failed to fetch orders:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const getStatusText = (status: string) => {
    const statusMap: Record<string, string> = {
      'DRAFT': '草稿',
      'PENDING_PAYMENT': '待付款',
      'PAID': '已付款',
      'SHIPPED': '已发货',
      'DELIVERED': '已送达',
      'COMPLETED': '已完成',
      'CANCELLED': '已取消',
      'REFUND_REQUESTED': '申请退款中',
      'REFUND_APPROVED': '退款已批准',
      'REFUNDED': '已退款'
    }
    return statusMap[status] || status
  }

  const getStatusColor = (status: string) => {
    const colorMap: Record<string, string> = {
      'DRAFT': 'bg-gray-100 text-gray-800',
      'PENDING_PAYMENT': 'bg-yellow-100 text-yellow-800',
      'PAID': 'bg-blue-100 text-blue-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'DELIVERED': 'bg-indigo-100 text-indigo-800',
      'COMPLETED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800',
      'REFUND_REQUESTED': 'bg-orange-100 text-orange-800',
      'REFUND_APPROVED': 'bg-orange-100 text-orange-800',
      'REFUNDED': 'bg-red-100 text-red-800'
    }
    return colorMap[status] || 'bg-gray-100 text-gray-800'
  }

  const getUserRole = (order: Order) => {
    if (!session?.user?.id) return ''
    return order.buyer.id === session.user.id ? 'buyer' : 'seller'
  }

  const getOtherParty = (order: Order) => {
    const role = getUserRole(order)
    return role === 'buyer' ? order.seller : order.buyer
  }

  // 订单操作函数
  const handleCancelOrder = async (orderId: string) => {
    if (!confirm('确定要取消这个订单吗？')) return

    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ status: 'CANCELLED' })
      })

      if (response.ok) {
        alert('订单已取消')
        fetchOrders()
      } else {
        const error = await response.json()
        alert(error.error || '取消订单失败')
      }
    } catch (error) {
      console.error('取消订单失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const handleConfirmReceipt = async (orderId: string) => {
    if (!confirm('确定已收到商品吗？确认后将无法撤销。')) return

    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ status: 'COMPLETED', receivedAt: new Date().toISOString() })
      })

      if (response.ok) {
        alert('已确认收货')
        fetchOrders()
      } else {
        const error = await response.json()
        alert(error.error || '确认收货失败')
      }
    } catch (error) {
      console.error('确认收货失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const handleExtendReceiptTime = async (orderId: string) => {
    if (!confirm('确定要延长收货时间吗？将延长7天。')) return

    try {
      const response = await fetch(`/api/orders/${orderId}/extend-receipt`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include'
      })

      if (response.ok) {
        alert('收货时间已延长7天')
        fetchOrders()
      } else {
        const error = await response.json()
        alert(error.error || '延长收货时间失败')
      }
    } catch (error) {
      console.error('延长收货时间失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const handleRequestRefund = async (orderId: string) => {
    const reason = prompt('请输入退款原因：')
    if (!reason) return

    try {
      const response = await fetch(`/api/orders/${orderId}/refund`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify({ reason })
      })

      if (response.ok) {
        alert('退款申请已提交')
        fetchOrders()
      } else {
        const error = await response.json()
        alert(error.error || '申请退款失败')
      }
    } catch (error) {
      console.error('申请退款失败:', error)
      alert('网络错误，请稍后重试')
    }
  }

  const handleContactSeller = (sellerId: string) => {
    router.push(`/chat?userId=${sellerId}`)
  }

  const handleContactBuyer = (buyerId: string) => {
    router.push(`/chat?userId=${buyerId}`)
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* 导航栏 */}
      <nav className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between h-16">
            <div className="flex items-center">
              <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
                <img
                  src="/logo.jpg"
                  alt="BitMarket Logo"
                  className="w-10 h-10 rounded-lg object-cover"
                />
              </Link>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/products" className="text-gray-700 hover:text-gray-900">
                商品列表
              </Link>
              <Link href="/profile" className="text-gray-700 hover:text-gray-900">
                个人中心
              </Link>
            </div>
          </div>
        </div>
      </nav>

      <div className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="mb-6">
            <h1 className="text-2xl font-bold text-gray-900">我的订单</h1>
          </div>

          {/* 筛选器 */}
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  订单类型
                </label>
                <select
                  value={filters.type}
                  onChange={(e) => setFilters(prev => ({ ...prev, type: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">全部订单</option>
                  <option value="buyer">我购买的</option>
                  <option value="seller">我出售的</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  订单状态
                </label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">全部状态</option>
                  <option value="PENDING_PAYMENT">待付款</option>
                  <option value="PAID">已付款</option>
                  <option value="SHIPPED">已发货</option>
                  <option value="DELIVERED">已送达</option>
                  <option value="COMPLETED">已完成</option>
                  <option value="CANCELLED">已取消</option>
                  <option value="REFUND_REQUESTED">申请退款中</option>
                  <option value="REFUNDED">已退款</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  搜索订单
                </label>
                <input
                  type="text"
                  value={filters.search}
                  onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                  placeholder="订单号或商品名称"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
              <div className="flex items-end">
                <button
                  onClick={() => setFilters({ type: 'all', status: '', search: '' })}
                  className="w-full px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-md"
                >
                  清空筛选
                </button>
              </div>
            </div>
          </div>

          {/* 订单列表 */}
          {isLoading ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">加载中...</div>
            </div>
          ) : orders.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-lg text-gray-600">暂无订单</div>
              <Link
                href="/products"
                className="mt-4 inline-block bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium"
              >
                去购买商品
              </Link>
            </div>
          ) : (
            <>
              <div className="space-y-4">
                {orders.map((order) => {
                  const role = getUserRole(order)
                  const otherParty = getOtherParty(order)
                  
                  return (
                    <div key={order.id} className="bg-white overflow-hidden shadow rounded-lg">
                      <div className="px-4 py-5 sm:p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className="flex items-center space-x-4">
                            <span className="text-sm font-medium text-gray-900">
                              订单号: {order.orderNumber}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                              {getStatusText(order.status)}
                            </span>
                            <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              role === 'buyer' ? 'bg-green-100 text-green-800' : 'bg-blue-100 text-blue-800'
                            }`}>
                              {role === 'buyer' ? '我购买的' : '我出售的'}
                            </span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {new Date(order.createdAt).toLocaleDateString()}
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="w-16 h-16 bg-gray-200 rounded-lg flex items-center justify-center">
                            <span className="text-gray-500">📦</span>
                          </div>
                          <div className="flex-1">
                            <h4 className="text-lg font-medium text-gray-900 mb-1">
                              {order.product.title}
                            </h4>
                            <div className="flex items-center justify-between">
                              <div className="text-sm text-gray-600">
                                {role === 'buyer' ? '卖家' : '买家'}: {otherParty.name || '匿名用户'}
                              </div>
                              <div className="text-lg font-bold text-blue-600">
                                {formatUSDT(order.totalAmount)}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* 物流跟踪信息 */}
                        {order.trackingNumber && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center justify-between">
                              <div className="text-sm">
                                <span className="text-gray-600">运单号: </span>
                                <span className="font-mono text-gray-900">{order.trackingNumber}</span>
                                {order.shippingCompany && (
                                  <span className="ml-2 text-blue-600">({order.shippingCompany})</span>
                                )}
                              </div>
                              {order.shippedAt && (
                                <div className="text-xs text-gray-500">
                                  发货时间: {new Date(order.shippedAt).toLocaleString()}
                                </div>
                              )}
                            </div>
                          </div>
                        )}

                        <div className="mt-4 flex justify-end space-x-2 flex-wrap">
                          <Link
                            href={`/orders/${order.id}`}
                            className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                          >
                            查看详情
                          </Link>

                          {/* 买家操作按钮 */}
                          {role === 'buyer' && (
                            <>
                              {order.status === 'PENDING_PAYMENT' && (
                                <>
                                  <Link
                                    href={`/order/${order.id}/info-collect`}
                                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                  >
                                    去付款
                                  </Link>
                                  <button
                                    onClick={() => handleCancelOrder(order.id)}
                                    className="bg-red-600 hover:bg-red-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                  >
                                    取消订单
                                  </button>
                                </>
                              )}

                              {(order.status === 'SHIPPED' || order.status === 'DELIVERED') && (
                                <>
                                  <button
                                    onClick={() => handleConfirmReceipt(order.id)}
                                    className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                  >
                                    确认收货
                                  </button>
                                  <button
                                    onClick={() => handleExtendReceiptTime(order.id)}
                                    className="bg-yellow-600 hover:bg-yellow-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                  >
                                    延长收货
                                  </button>
                                </>
                              )}

                              {['PAID', 'SHIPPED', 'DELIVERED'].includes(order.status) && (
                                <button
                                  onClick={() => handleRequestRefund(order.id)}
                                  className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                >
                                  申请退款
                                </button>
                              )}

                              <button
                                onClick={() => handleContactSeller(order.seller.id)}
                                className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                              >
                                联系卖家
                              </button>
                            </>
                          )}

                          {/* 卖家操作按钮 */}
                          {role === 'seller' && (
                            <>
                              {order.status === 'PAID' && (
                                <Link
                                  href={`/orders/${order.id}/shipping`}
                                  className="bg-green-600 hover:bg-green-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                                >
                                  去发货
                                </Link>
                              )}

                              <button
                                onClick={() => handleContactBuyer(order.buyer.id)}
                                className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1.5 rounded-md text-sm font-medium"
                              >
                                联系买家
                              </button>
                            </>
                          )}
                        </div>
                      </div>
                    </div>
                  )
                })}
              </div>

              {/* 分页 */}
              {pagination.pages > 1 && (
                <div className="mt-8 flex justify-center">
                  <nav className="flex space-x-2">
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.max(1, prev.page - 1) }))}
                      disabled={pagination.page === 1}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      上一页
                    </button>
                    
                    <span className="px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md">
                      {pagination.page} / {pagination.pages}
                    </span>
                    
                    <button
                      onClick={() => setPagination(prev => ({ ...prev, page: Math.min(prev.pages, prev.page + 1) }))}
                      disabled={pagination.page === pagination.pages}
                      className="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      下一页
                    </button>
                  </nav>
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
