'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  createdAt: string
  shippedAt?: string
  trackingNumber?: string
  shippingCompany?: string
  estimatedDelivery?: string
  shippingNotes?: string
  product: {
    id: string
    title: string
    images: string
    price: number
  }
  buyer: {
    id: string
    name: string
    email: string
  }
  seller: {
    id: string
    name: string
    email: string
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

export default function PrintOrderPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [printTemplate, setPrintTemplate] = useState<'shipping' | 'invoice' | 'combined'>('shipping')

  const orderId = params.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/orders/seller')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查权限：只有卖家可以打印发货单
      if (orderData.seller.id !== session?.user?.id) {
        alert('无权限访问此页面')
        router.push('/orders')
        return
      }
      
      setOrder(orderData)
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handlePrint = () => {
    window.print()
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString('zh-CN')
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/orders/seller')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回订单列表
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-white">
      {/* 打印控制栏 - 仅在屏幕上显示 */}
      <div className="print:hidden bg-gray-100 p-4 border-b">
        <div className="max-w-4xl mx-auto flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <div className="flex items-center space-x-2">
              <label className="text-sm font-medium text-gray-700">打印模板:</label>
              <select
                value={printTemplate}
                onChange={(e) => setPrintTemplate(e.target.value as any)}
                className="px-3 py-1 border border-gray-300 rounded text-sm"
              >
                <option value="shipping">发货单</option>
                <option value="invoice">发票</option>
                <option value="combined">综合单据</option>
              </select>
            </div>
          </div>
          <button
            onClick={handlePrint}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            打印
          </button>
        </div>
      </div>

      {/* 打印内容 */}
      <div className="max-w-4xl mx-auto p-8">
        {printTemplate === 'shipping' && (
          <div className="shipping-label">
            {/* 发货单模板 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold">发货单</h1>
              <div className="text-lg text-gray-600 mt-2">SHIPPING LABEL</div>
            </div>

            <div className="grid grid-cols-2 gap-8 mb-8">
              {/* 发件人信息 */}
              <div className="border-2 border-gray-300 p-4">
                <h3 className="font-bold text-lg mb-3 bg-gray-100 p-2">发件人 (FROM)</h3>
                <div className="space-y-2">
                  <div><strong>姓名:</strong> {order.seller.name}</div>
                  <div><strong>电话:</strong> 商家联系电话</div>
                  <div><strong>地址:</strong> 商家发货地址</div>
                </div>
              </div>

              {/* 收件人信息 */}
              <div className="border-2 border-gray-300 p-4">
                <h3 className="font-bold text-lg mb-3 bg-gray-100 p-2">收件人 (TO)</h3>
                <div className="space-y-2">
                  <div><strong>姓名:</strong> {order.shippingAddress.name}</div>
                  <div><strong>电话:</strong> {order.shippingAddress.phone}</div>
                  <div><strong>地址:</strong> {order.shippingAddress.province} {order.shippingAddress.city} {order.shippingAddress.district} {order.shippingAddress.detail}</div>
                </div>
              </div>
            </div>

            {/* 物流信息 */}
            <div className="border-2 border-gray-300 p-4 mb-8">
              <h3 className="font-bold text-lg mb-3 bg-gray-100 p-2">物流信息</h3>
              <div className="grid grid-cols-2 gap-4">
                <div><strong>快递公司:</strong> {order.shippingCompany || '待填写'}</div>
                <div><strong>运单号:</strong> {order.trackingNumber || '待填写'}</div>
                <div><strong>发货时间:</strong> {order.shippedAt ? formatDate(order.shippedAt) : '待发货'}</div>
                <div><strong>预计送达:</strong> {order.estimatedDelivery ? formatDate(order.estimatedDelivery) : '待确定'}</div>
              </div>
              {order.shippingNotes && (
                <div className="mt-3">
                  <strong>备注:</strong> {order.shippingNotes}
                </div>
              )}
            </div>

            {/* 商品信息 */}
            <div className="border-2 border-gray-300 p-4 mb-8">
              <h3 className="font-bold text-lg mb-3 bg-gray-100 p-2">商品清单</h3>
              <table className="w-full border-collapse">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-2">商品名称</th>
                    <th className="text-center p-2">数量</th>
                    <th className="text-right p-2">单价</th>
                    <th className="text-right p-2">小计</th>
                  </tr>
                </thead>
                <tbody>
                  <tr>
                    <td className="p-2">{order.product.title}</td>
                    <td className="text-center p-2">{order.metadata.quantity}</td>
                    <td className="text-right p-2">¥{order.metadata.itemPrice.toFixed(2)}</td>
                    <td className="text-right p-2">¥{order.productPrice.toFixed(2)}</td>
                  </tr>
                </tbody>
              </table>
            </div>

            {/* 订单信息 */}
            <div className="grid grid-cols-2 gap-8">
              <div>
                <h3 className="font-bold text-lg mb-3">订单信息</h3>
                <div className="space-y-2">
                  <div><strong>订单号:</strong> {order.orderNumber}</div>
                  <div><strong>下单时间:</strong> {formatDate(order.createdAt)}</div>
                  <div><strong>订单状态:</strong> {order.status}</div>
                </div>
              </div>
              <div>
                <h3 className="font-bold text-lg mb-3">费用明细</h3>
                <div className="space-y-2">
                  <div><strong>商品金额:</strong> ¥{order.productPrice.toFixed(2)}</div>
                  <div><strong>运费:</strong> ¥{order.shippingFee.toFixed(2)}</div>
                  <div className="border-t pt-2"><strong>总计:</strong> ¥{order.totalAmount.toFixed(2)}</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {printTemplate === 'invoice' && (
          <div className="invoice">
            {/* 发票模板 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold">销售发票</h1>
              <div className="text-lg text-gray-600 mt-2">SALES INVOICE</div>
            </div>
            {/* 发票内容... */}
          </div>
        )}

        {printTemplate === 'combined' && (
          <div className="combined">
            {/* 综合单据模板 */}
            <div className="text-center mb-8">
              <h1 className="text-3xl font-bold">订单处理单</h1>
              <div className="text-lg text-gray-600 mt-2">ORDER PROCESSING FORM</div>
            </div>
            {/* 综合单据内容... */}
          </div>
        )}
      </div>

      {/* 打印样式 */}
      <style jsx>{`
        @media print {
          body {
            margin: 0;
            padding: 0;
          }
          .print\\:hidden {
            display: none !important;
          }
        }
      `}</style>
    </div>
  )
}
