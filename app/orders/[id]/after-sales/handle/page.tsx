'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import Link from 'next/link'
import { Navbar } from '@/components/Navbar'

interface AfterSalesRequest {
  id: string
  type: string
  reason: string
  description: string
  requestedAmount?: number
  images?: string[]
  status: string
  createdAt: string
  buyer: {
    name: string
  }
  order: {
    id: string
    orderNumber: string
    totalAmount: number
    product: {
      title: string
      images: string
    }
  }
}

export default function HandleAfterSalesPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [request, setRequest] = useState<AfterSalesRequest | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  
  // 处理表单
  const [handleForm, setHandleForm] = useState({
    action: '', // APPROVE, REJECT
    response: '',
    refundAmount: '',
    exchangeProductId: ''
  })

  const orderId = params.id as string
  const requestId = new URLSearchParams(window.location.search).get('requestId')

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId && requestId) {
      loadAfterSalesRequest()
    }
  }, [session, orderId, requestId])

  const loadAfterSalesRequest = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}/after-sales`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        throw new Error('获取售后申请失败')
      }
      
      const data = await response.json()
      const targetRequest = data.requests.find((req: any) => req.id === requestId)
      
      if (!targetRequest) {
        alert('售后申请不存在')
        router.push(`/orders/${orderId}`)
        return
      }

      // 检查权限：只有卖家可以处理
      if (targetRequest.sellerId !== session?.user?.id) {
        alert('无权限处理此售后申请')
        router.push(`/orders/${orderId}`)
        return
      }

      // 检查状态
      if (targetRequest.status !== 'PENDING') {
        alert('该售后申请已被处理')
        router.push(`/orders/${orderId}`)
        return
      }
      
      setRequest(targetRequest)
      
      // 如果是退款申请，默认设置退款金额
      if (targetRequest.type === 'REFUND') {
        setHandleForm(prev => ({
          ...prev,
          refundAmount: targetRequest.requestedAmount?.toString() || ''
        }))
      }
      
    } catch (error) {
      console.error('加载售后申请失败:', error)
      alert('加载售后申请失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!handleForm.action) {
      alert('请选择处理方式')
      return
    }
    
    if (!handleForm.response.trim()) {
      alert('请填写回复内容')
      return
    }

    if (handleForm.action === 'APPROVE' && request?.type === 'REFUND') {
      const refundAmount = parseFloat(handleForm.refundAmount)
      if (isNaN(refundAmount) || refundAmount <= 0 || refundAmount > (request.requestedAmount || 0)) {
        alert('退款金额不正确')
        return
      }
    }
    
    const confirmMessage = handleForm.action === 'APPROVE' 
      ? '确定要同意此售后申请吗？' 
      : '确定要拒绝此售后申请吗？'
    
    if (!confirm(confirmMessage)) {
      return
    }
    
    setSubmitting(true)
    
    try {
      const response = await fetch(`/api/orders/${orderId}/after-sales`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          requestId: request?.id,
          action: handleForm.action,
          response: handleForm.response.trim(),
          refundAmount: handleForm.action === 'APPROVE' && request?.type === 'REFUND' 
            ? parseFloat(handleForm.refundAmount) 
            : null,
          exchangeProductId: handleForm.action === 'APPROVE' && request?.type === 'EXCHANGE' 
            ? handleForm.exchangeProductId || null 
            : null
        })
      })
      
      if (response.ok) {
        alert(`售后申请已${handleForm.action === 'APPROVE' ? '同意' : '拒绝'}`)
        router.push(`/orders/${orderId}`)
      } else {
        const error = await response.json()
        alert(error.error || '处理售后申请失败')
      }
    } catch (error) {
      console.error('处理售后申请失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  const getTypeText = (type: string) => {
    const typeMap: Record<string, string> = {
      'REFUND': '退款',
      'EXCHANGE': '换货',
      'REPAIR': '维修'
    }
    return typeMap[type] || type
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!request) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">售后申请不存在</h2>
            <button
              onClick={() => router.push(`/orders/${orderId}`)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单详情
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">处理售后申请</h1>
          </div>
          <div className="text-sm text-gray-600">
            订单号：{request.order.orderNumber}
          </div>
        </div>

        {/* 售后申请信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">售后申请详情</h3>
          
          <div className="space-y-4">
            {/* 基本信息 */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span className="text-sm text-gray-600">申请类型:</span>
                <span className="ml-2 font-medium text-orange-600">{getTypeText(request.type)}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">申请时间:</span>
                <span className="ml-2">{new Date(request.createdAt).toLocaleString()}</span>
              </div>
              <div>
                <span className="text-sm text-gray-600">买家:</span>
                <span className="ml-2">{request.buyer.name}</span>
              </div>
              {request.type === 'REFUND' && request.requestedAmount && (
                <div>
                  <span className="text-sm text-gray-600">申请退款金额:</span>
                  <span className="ml-2 font-medium text-red-600">¥{request.requestedAmount.toFixed(2)}</span>
                </div>
              )}
            </div>

            {/* 申请原因 */}
            <div>
              <span className="text-sm text-gray-600">申请原因:</span>
              <span className="ml-2">{request.reason}</span>
            </div>

            {/* 问题描述 */}
            <div>
              <span className="text-sm text-gray-600">问题描述:</span>
              <div className="mt-2 p-3 bg-gray-50 rounded-lg">
                <p className="text-gray-700">{request.description}</p>
              </div>
            </div>

            {/* 问题图片 */}
            {request.images && request.images.length > 0 && (
              <div>
                <span className="text-sm text-gray-600">问题图片:</span>
                <div className="mt-2 grid grid-cols-3 md:grid-cols-6 gap-4">
                  {request.images.map((image, index) => (
                    <img
                      key={index}
                      src={image}
                      alt={`问题图片 ${index + 1}`}
                      className="w-full h-20 object-cover rounded-lg cursor-pointer"
                      onClick={() => window.open(image, '_blank')}
                    />
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 商品信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">相关商品</h3>
          <div className="flex items-center space-x-4">
            <img
              src={request.order.product.images?.split(',')[0] || '/placeholder.jpg'}
              alt={request.order.product.title}
              className="w-20 h-20 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{request.order.product.title}</h4>
              <div className="text-sm text-gray-600 mt-1">
                订单金额: ¥{request.order.totalAmount.toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {/* 处理表单 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">处理售后申请</h3>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 处理方式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                处理方式 *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    handleForm.action === 'APPROVE'
                      ? 'border-green-500 bg-green-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onClick={() => setHandleForm({...handleForm, action: 'APPROVE'})}
                >
                  <div className="flex items-center mb-2">
                    <input
                      type="radio"
                      name="action"
                      value="APPROVE"
                      checked={handleForm.action === 'APPROVE'}
                      onChange={() => setHandleForm({...handleForm, action: 'APPROVE'})}
                      className="mr-2"
                    />
                    <span className="font-medium text-green-600">同意申请</span>
                  </div>
                  <p className="text-sm text-gray-600">同意买家的售后申请，按要求处理</p>
                </div>
                
                <div
                  className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                    handleForm.action === 'REJECT'
                      ? 'border-red-500 bg-red-50'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                  onClick={() => setHandleForm({...handleForm, action: 'REJECT'})}
                >
                  <div className="flex items-center mb-2">
                    <input
                      type="radio"
                      name="action"
                      value="REJECT"
                      checked={handleForm.action === 'REJECT'}
                      onChange={() => setHandleForm({...handleForm, action: 'REJECT'})}
                      className="mr-2"
                    />
                    <span className="font-medium text-red-600">拒绝申请</span>
                  </div>
                  <p className="text-sm text-gray-600">拒绝买家的售后申请，需说明理由</p>
                </div>
              </div>
            </div>

            {/* 退款金额（同意退款时显示） */}
            {handleForm.action === 'APPROVE' && request.type === 'REFUND' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  实际退款金额 *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">¥</span>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={request.requestedAmount}
                    value={handleForm.refundAmount}
                    onChange={(e) => setHandleForm({...handleForm, refundAmount: e.target.value})}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  买家申请金额：¥{request.requestedAmount?.toFixed(2)}
                </p>
              </div>
            )}

            {/* 回复内容 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                回复内容 *
              </label>
              <textarea
                value={handleForm.response}
                onChange={(e) => setHandleForm({...handleForm, response: e.target.value})}
                placeholder={handleForm.action === 'APPROVE' ? '请说明处理方案和后续步骤' : '请说明拒绝理由'}
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
              >
                取消
              </button>
              <Link
                href={`/after-sales/${request?.id}/chat`}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center"
              >
                与买家沟通
              </Link>
              {request?.type === 'REFUND' && request?.status === 'APPROVED' && (
                <Link
                  href={`/after-sales/${request?.id}/refund`}
                  className="flex-1 bg-purple-600 hover:bg-purple-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center"
                >
                  处理退款
                </Link>
              )}
              {request?.type === 'EXCHANGE' && ['APPROVED', 'PROCESSING'].includes(request?.status) && (
                <Link
                  href={`/after-sales/${request?.id}/exchange`}
                  className="flex-1 bg-indigo-600 hover:bg-indigo-700 text-white px-6 py-3 rounded-md text-lg font-medium text-center"
                >
                  处理换货
                </Link>
              )}
              <button
                type="submit"
                disabled={submitting}
                className={`flex-1 px-6 py-3 rounded-md text-lg font-medium text-white ${
                  handleForm.action === 'APPROVE'
                    ? 'bg-green-600 hover:bg-green-700 disabled:bg-gray-400'
                    : 'bg-red-600 hover:bg-red-700 disabled:bg-gray-400'
                }`}
              >
                {submitting ? '处理中...' : (handleForm.action === 'APPROVE' ? '同意申请' : '拒绝申请')}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
