'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  createdAt: string
  shippedAt?: string
  receivedAt?: string
  product: {
    id: string
    title: string
    images: string
    price: number
  }
  seller: {
    id: string
    name: string
  }
  buyer: {
    id: string
    name: string
  }
  metadata: {
    quantity: number
    itemPrice: number
  }
}

export default function AfterSalesPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [submitting, setSubmitting] = useState(false)
  
  // 售后申请表单
  const [afterSalesForm, setAfterSalesForm] = useState({
    type: '', // REFUND, EXCHANGE, REPAIR
    reason: '',
    description: '',
    requestedAmount: '',
    images: [] as File[]
  })

  const orderId = params.id as string

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/orders')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查权限：只有买家可以申请售后
      if (orderData.buyer.id !== session?.user?.id) {
        alert('无权限访问此页面')
        router.push('/orders')
        return
      }

      // 检查订单状态：只有已发货或已收货的订单可以申请售后
      if (!['SHIPPED', 'DELIVERED', 'COMPLETED'].includes(orderData.status)) {
        alert('当前订单状态不支持申请售后')
        router.push(`/orders/${orderId}`)
        return
      }
      
      setOrder(orderData)
      
      // 如果是退款申请，默认设置退款金额
      if (afterSalesForm.type === 'REFUND') {
        setAfterSalesForm(prev => ({
          ...prev,
          requestedAmount: orderData.totalAmount.toString()
        }))
      }
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const handleTypeChange = (type: string) => {
    setAfterSalesForm(prev => ({
      ...prev,
      type,
      requestedAmount: type === 'REFUND' ? order?.totalAmount.toString() || '' : ''
    }))
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length + afterSalesForm.images.length > 6) {
      alert('最多只能上传6张图片')
      return
    }
    
    setAfterSalesForm(prev => ({
      ...prev,
      images: [...prev.images, ...files]
    }))
  }

  const removeImage = (index: number) => {
    setAfterSalesForm(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!afterSalesForm.type) {
      alert('请选择售后类型')
      return
    }
    
    if (!afterSalesForm.reason) {
      alert('请选择申请原因')
      return
    }
    
    if (!afterSalesForm.description.trim()) {
      alert('请详细描述问题')
      return
    }

    if (afterSalesForm.type === 'REFUND' && !afterSalesForm.requestedAmount) {
      alert('请输入退款金额')
      return
    }

    if (afterSalesForm.type === 'REFUND') {
      const requestedAmount = parseFloat(afterSalesForm.requestedAmount)
      if (isNaN(requestedAmount) || requestedAmount <= 0 || requestedAmount > (order?.totalAmount || 0)) {
        alert('退款金额不正确')
        return
      }
    }
    
    if (!confirm('确定要提交售后申请吗？')) {
      return
    }
    
    setSubmitting(true)
    
    try {
      // 上传图片
      let imageUrls: string[] = []
      if (afterSalesForm.images.length > 0) {
        const formData = new FormData()
        afterSalesForm.images.forEach((file, index) => {
          formData.append(`image_${index}`, file)
        })
        
        const uploadResponse = await fetch('/api/upload/after-sales-images', {
          method: 'POST',
          credentials: 'include',
          body: formData
        })
        
        if (uploadResponse.ok) {
          const uploadData = await uploadResponse.json()
          imageUrls = uploadData.urls
        }
      }

      // 提交售后申请
      const response = await fetch(`/api/orders/${orderId}/after-sales`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          type: afterSalesForm.type,
          reason: afterSalesForm.reason,
          description: afterSalesForm.description.trim(),
          requestedAmount: afterSalesForm.type === 'REFUND' ? parseFloat(afterSalesForm.requestedAmount) : null,
          images: imageUrls
        })
      })
      
      if (response.ok) {
        alert('售后申请提交成功，请等待卖家处理')
        router.push(`/orders/${orderId}`)
      } else {
        const error = await response.json()
        alert(error.error || '提交售后申请失败')
      }
    } catch (error) {
      console.error('提交售后申请失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setSubmitting(false)
    }
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <div className="text-lg">加载中...</div>
          </div>
        </div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Navbar />
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
            <button
              onClick={() => router.push('/orders')}
              className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
            >
              返回订单列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-4xl mx-auto px-4 py-8">
        {/* 页面标题 */}
        <div className="mb-6">
          <div className="flex items-center space-x-4 mb-4">
            <button
              onClick={() => router.back()}
              className="text-blue-600 hover:text-blue-700"
            >
              ← 返回
            </button>
            <h1 className="text-2xl font-bold text-gray-900">申请售后服务</h1>
          </div>
          <div className="text-sm text-gray-600">
            订单号：{order.orderNumber}
          </div>
        </div>

        {/* 订单信息 */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">订单信息</h3>
          <div className="flex items-center space-x-4">
            <img
              src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
              alt={order.product.title}
              className="w-20 h-20 object-cover rounded-lg"
            />
            <div className="flex-1">
              <h4 className="font-medium text-gray-900">{order.product.title}</h4>
              <div className="text-sm text-gray-600 mt-1">
                数量: {order.metadata.quantity} | 单价: ¥{order.metadata.itemPrice.toFixed(2)}
              </div>
              <div className="text-sm font-medium text-blue-600 mt-1">
                订单金额: ¥{order.totalAmount.toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {/* 售后申请表单 */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-6">售后申请</h3>
          
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* 售后类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-3">
                售后类型 *
              </label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {[
                  { value: 'REFUND', label: '申请退款', desc: '商品有质量问题或不符合描述' },
                  { value: 'EXCHANGE', label: '申请换货', desc: '商品有瑕疵，需要更换同款商品' },
                  { value: 'REPAIR', label: '申请维修', desc: '商品功能异常，需要维修服务' }
                ].map((type) => (
                  <div
                    key={type.value}
                    className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                      afterSalesForm.type === type.value
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                    onClick={() => handleTypeChange(type.value)}
                  >
                    <div className="flex items-center mb-2">
                      <input
                        type="radio"
                        name="type"
                        value={type.value}
                        checked={afterSalesForm.type === type.value}
                        onChange={() => handleTypeChange(type.value)}
                        className="mr-2"
                      />
                      <span className="font-medium">{type.label}</span>
                    </div>
                    <p className="text-sm text-gray-600">{type.desc}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* 申请原因 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                申请原因 *
              </label>
              <select
                value={afterSalesForm.reason}
                onChange={(e) => setAfterSalesForm({...afterSalesForm, reason: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              >
                <option value="">请选择申请原因</option>
                <option value="质量问题">商品质量问题</option>
                <option value="描述不符">商品与描述不符</option>
                <option value="尺寸不合适">尺寸不合适</option>
                <option value="发错商品">发错商品</option>
                <option value="包装破损">包装破损</option>
                <option value="功能异常">功能异常</option>
                <option value="其他原因">其他原因</option>
              </select>
            </div>

            {/* 退款金额（仅退款时显示） */}
            {afterSalesForm.type === 'REFUND' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  申请退款金额 *
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-2 text-gray-500">¥</span>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    max={order.totalAmount}
                    value={afterSalesForm.requestedAmount}
                    onChange={(e) => setAfterSalesForm({...afterSalesForm, requestedAmount: e.target.value})}
                    className="w-full pl-8 pr-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                    required
                  />
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  最大退款金额：¥{order.totalAmount.toFixed(2)}
                </p>
              </div>
            )}

            {/* 问题描述 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                问题描述 *
              </label>
              <textarea
                value={afterSalesForm.description}
                onChange={(e) => setAfterSalesForm({...afterSalesForm, description: e.target.value})}
                placeholder="请详细描述遇到的问题，以便卖家更好地为您处理"
                rows={4}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                required
              />
            </div>

            {/* 图片上传 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                问题图片（可选）
              </label>
              <div className="space-y-4">
                {/* 已上传的图片 */}
                {afterSalesForm.images.length > 0 && (
                  <div className="grid grid-cols-3 md:grid-cols-6 gap-4">
                    {afterSalesForm.images.map((file, index) => (
                      <div key={index} className="relative">
                        <img
                          src={URL.createObjectURL(file)}
                          alt={`问题图片 ${index + 1}`}
                          className="w-full h-20 object-cover rounded-lg"
                        />
                        <button
                          type="button"
                          onClick={() => removeImage(index)}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                        >
                          ×
                        </button>
                      </div>
                    ))}
                  </div>
                )}
                
                {/* 上传按钮 */}
                {afterSalesForm.images.length < 6 && (
                  <div>
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <label
                      htmlFor="image-upload"
                      className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer"
                    >
                      <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      上传图片
                    </label>
                    <p className="text-xs text-gray-500 mt-1">
                      最多上传6张图片，支持JPG、PNG格式
                    </p>
                  </div>
                )}
              </div>
            </div>

            {/* 提交按钮 */}
            <div className="flex space-x-4">
              <button
                type="button"
                onClick={() => router.back()}
                className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
              >
                取消
              </button>
              <button
                type="submit"
                disabled={submitting}
                className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
              >
                {submitting ? '提交中...' : '提交申请'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
