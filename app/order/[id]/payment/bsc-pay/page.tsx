'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Navbar } from '@/components/Navbar'

interface Order {
  id: string
  orderNumber: string
  status: string
  totalAmount: number
  productPrice: number
  shippingFee: number
  platformFee: number
  product: {
    id: string
    title: string
    images: string
    seller: {
      id: string
      name: string
    }
  }
  shippingAddress: {
    name: string
    phone: string
    province: string
    city: string
    district: string
    detail: string
  }
  metadata: {
    quantity: number
    variantId?: string
    itemPrice: number
  }
}

export default function BSCPayPage() {
  const router = useRouter()
  const params = useParams()
  const { data: session } = useSession()
  
  const [order, setOrder] = useState<Order | null>(null)
  const [loading, setLoading] = useState(true)
  const [paymentProcessing, setPaymentProcessing] = useState(false)
  const [walletAddress, setWalletAddress] = useState<string>('')
  const [paymentTimeout, setPaymentTimeout] = useState(900) // 15分钟倒计时
  const [txHash, setTxHash] = useState<string>('')

  const orderId = params.id as string

  // BSC网络配置
  const BSC_NETWORK = {
    chainId: '0x38', // 56 in hex
    chainName: 'BNB Smart Chain',
    nativeCurrency: {
      name: 'BNB',
      symbol: 'BNB',
      decimals: 18,
    },
    rpcUrls: ['https://bsc-dataseed.binance.org/'],
    blockExplorerUrls: ['https://bscscan.com/'],
  }

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
    
    if (orderId) {
      loadOrderData()
    }
  }, [session, orderId])

  useEffect(() => {
    // 支付倒计时
    if (paymentTimeout > 0) {
      const timer = setTimeout(() => {
        setPaymentTimeout(paymentTimeout - 1)
      }, 1000)
      return () => clearTimeout(timer)
    } else {
      // 支付超时，返回信息收集页面
      alert('支付超时，请重新选择支付方式')
      router.push(`/order/${orderId}/info-collect`)
    }
  }, [paymentTimeout, orderId, router])

  const loadOrderData = async () => {
    try {
      const response = await fetch(`/api/orders/${orderId}`, {
        credentials: 'include'
      })
      
      if (!response.ok) {
        if (response.status === 404) {
          alert('订单不存在')
          router.push('/products')
          return
        }
        throw new Error('获取订单信息失败')
      }
      
      const orderData = await response.json()
      
      // 检查订单状态
      if (orderData.status !== 'PENDING_PAYMENT') {
        alert('订单状态异常')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      // 检查支付方式
      if (orderData.paymentMethod !== 'bsc-pay') {
        alert('支付方式不匹配')
        router.push(`/order/${orderId}/info-collect`)
        return
      }
      
      setOrder(orderData)
      generateWalletAddress()
      
    } catch (error) {
      console.error('加载订单数据失败:', error)
      alert('加载订单信息失败，请稍后重试')
    } finally {
      setLoading(false)
    }
  }

  const generateWalletAddress = async () => {
    try {
      // 这里应该生成或获取收款钱包地址
      // 暂时使用占位符地址
      setWalletAddress('******************************************')
    } catch (error) {
      console.error('生成钱包地址失败:', error)
      alert('生成收款地址失败，请稍后重试')
    }
  }

  const connectWallet = async () => {
    try {
      if (typeof window.ethereum !== 'undefined') {
        // 请求连接钱包
        await window.ethereum.request({ method: 'eth_requestAccounts' })
        
        // 检查网络
        const chainId = await window.ethereum.request({ method: 'eth_chainId' })
        if (chainId !== BSC_NETWORK.chainId) {
          // 切换到BSC网络
          try {
            await window.ethereum.request({
              method: 'wallet_switchEthereumChain',
              params: [{ chainId: BSC_NETWORK.chainId }],
            })
          } catch (switchError: any) {
            // 如果网络不存在，添加网络
            if (switchError.code === 4902) {
              await window.ethereum.request({
                method: 'wallet_addEthereumChain',
                params: [BSC_NETWORK],
              })
            }
          }
        }
        
        alert('钱包连接成功！请手动转账到指定地址')
      } else {
        alert('请安装MetaMask或其他Web3钱包')
      }
    } catch (error) {
      console.error('连接钱包失败:', error)
      alert('连接钱包失败，请重试')
    }
  }

  const handlePaymentConfirm = async () => {
    if (!txHash.trim()) {
      alert('请输入交易哈希')
      return
    }

    // 简单验证交易哈希格式
    if (!txHash.startsWith('0x') || txHash.length !== 66) {
      alert('交易哈希格式不正确，应该以0x开头且长度为66位')
      return
    }

    setPaymentProcessing(true)

    try {
      const response = await fetch(`/api/orders/${orderId}/payment`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          paymentMethod: 'bsc-pay',
          paymentTxHash: txHash
        })
      })

      if (response.ok) {
        const result = await response.json()
        alert('支付提交成功，等待管理员审核确认')
        // 跳转到成功页面
        router.push(`/order/${orderId}/payment/successful`)
      } else {
        const error = await response.json()
        alert(error.error || '支付确认失败')
      }
    } catch (error) {
      console.error('支付确认失败:', error)
      alert('网络错误，请稍后重试')
    } finally {
      setPaymentProcessing(false)
    }
  }

  const copyAddress = () => {
    navigator.clipboard.writeText(walletAddress)
    alert('地址已复制到剪贴板')
  }

  const formatTime = (seconds: number) => {
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`
  }

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">请先登录</h2>
          <button
            onClick={() => router.push('/auth/signin')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            去登录
          </button>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-lg">加载中...</div>
      </div>
    )
  }

  if (!order) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">订单不存在</h2>
          <button
            onClick={() => router.push('/products')}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md"
          >
            返回商品列表
          </button>
        </div>
      </div>
    )
  }

  // 计算USDT金额（假设1 USDT = 1 CNY）
  const usdtAmount = order.totalAmount

  return (
    <div className="min-h-screen bg-gray-50">
      <Navbar />
      
      <div className="max-w-2xl mx-auto px-4 py-8">
        <div className="bg-white rounded-lg shadow-md p-6">
          <div className="text-center mb-6">
            <h1 className="text-2xl font-bold mb-2">BNB Smart Chain支付</h1>
            <div className="text-lg text-blue-600 font-semibold">
              支付金额：{usdtAmount.toFixed(2)} USDT
            </div>
            <div className="text-sm text-gray-600 mt-2">
              订单号：{order.orderNumber}
            </div>
          </div>

          {/* 支付倒计时 */}
          <div className="text-center mb-6">
            <div className="inline-flex items-center px-4 py-2 bg-orange-100 text-orange-800 rounded-lg">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
              </svg>
              请在 {formatTime(paymentTimeout)} 内完成支付
            </div>
          </div>

          {/* 收款地址 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">收款地址</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm text-gray-600">BSC网络 USDT地址</span>
                <button
                  onClick={copyAddress}
                  className="text-blue-600 hover:text-blue-700 text-sm"
                >
                  复制地址
                </button>
              </div>
              <div className="font-mono text-sm bg-white p-3 rounded border break-all">
                {walletAddress}
              </div>
            </div>
          </div>

          {/* 支付说明 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">支付说明</h3>
            <div className="bg-blue-50 rounded-lg p-4">
              <ol className="list-decimal list-inside space-y-2 text-sm text-gray-700">
                <li>确保您的钱包已连接到BNB Smart Chain网络</li>
                <li>向上方地址转账 {usdtAmount.toFixed(2)} USDT</li>
                <li>复制交易哈希并粘贴到下方输入框</li>
                <li>点击"确认支付"按钮提交支付信息</li>
                <li>等待管理员审核确认支付</li>
              </ol>
            </div>
          </div>

          {/* 连接钱包按钮 */}
          <div className="mb-6">
            <button
              onClick={connectWallet}
              className="w-full bg-yellow-500 hover:bg-yellow-600 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              连接钱包
            </button>
          </div>

          {/* 交易哈希输入 */}
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              交易哈希 (Transaction Hash) *
            </label>
            <input
              type="text"
              value={txHash}
              onChange={(e) => setTxHash(e.target.value)}
              placeholder="请输入交易哈希，例如：0x..."
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
            />
            <p className="text-xs text-gray-500 mt-1">
              转账完成后，请将交易哈希粘贴到此处
            </p>
          </div>

          {/* 订单信息 */}
          <div className="mb-8">
            <h3 className="font-medium mb-3">订单信息</h3>
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center space-x-4 mb-4">
                <img
                  src={order.product.images?.split(',')[0] || '/placeholder.jpg'}
                  alt={order.product.title}
                  className="w-16 h-16 object-cover rounded"
                />
                <div className="flex-1">
                  <h4 className="font-medium">{order.product.title}</h4>
                  <p className="text-gray-600 text-sm">卖家：{order.product.seller.name}</p>
                  <p className="text-gray-600 text-sm">数量：{order.metadata.quantity}</p>
                </div>
                <div className="text-right">
                  <p className="font-semibold">¥{order.productPrice.toFixed(2)}</p>
                </div>
              </div>
              
              <div className="border-t pt-4">
                <div className="flex justify-between text-sm mb-1">
                  <span>商品金额</span>
                  <span>¥{order.productPrice.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm mb-1">
                  <span>运费</span>
                  <span>¥{order.shippingFee.toFixed(2)}</span>
                </div>
                <div className="flex justify-between font-semibold text-lg pt-2 border-t">
                  <span>总计</span>
                  <span className="text-blue-600">{usdtAmount.toFixed(2)} USDT</span>
                </div>
              </div>
            </div>
          </div>

          {/* 操作按钮 */}
          <div className="flex space-x-4">
            <button
              onClick={() => router.push(`/order/${orderId}/info-collect`)}
              className="flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700 px-6 py-3 rounded-md text-lg font-medium"
            >
              返回
            </button>
            <button
              onClick={handlePaymentConfirm}
              disabled={paymentProcessing || !txHash.trim()}
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white px-6 py-3 rounded-md text-lg font-medium"
            >
              {paymentProcessing ? '确认中...' : '确认支付'}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}
