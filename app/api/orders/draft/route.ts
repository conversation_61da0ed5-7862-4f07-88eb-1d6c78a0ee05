import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { canUserCreateOrder } from '@/lib/user-status'
import { generateOrderNumber } from '@/lib/utils'

// 创建草稿订单
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    // 严格的身份验证
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录，请先登录' },
        { status: 401 }
      )
    }

    // 验证用户存在性和状态
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        status: true,
        bannedAt: true,
        bannedUntil: true,
        email: true,
        name: true
      }
    })

    if (!user) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 401 }
      )
    }

    // 检查用户是否可以创建订单
    const canCreate = await canUserCreateOrder(session.user.id)
    if (!canCreate) {
      return NextResponse.json(
        { error: '您的账户状态不允许创建订单，请联系客服' },
        { status: 403 }
      )
    }

    const body = await request.json()
    const {
      productId,
      variantId,
      quantity = 1,
      useEscrow = false
    } = body

    // 验证必填字段
    if (!productId) {
      return NextResponse.json(
        { error: '商品ID为必填项' },
        { status: 400 }
      )
    }

    // 获取商品信息
    const product = await prisma.product.findUnique({
      where: { id: productId },
      include: {
        seller: {
          select: {
            id: true,
            name: true
          }
        },
        variants: {
          include: {
            attributes: true
          }
        }
      }
    })

    if (!product) {
      return NextResponse.json(
        { error: '商品不存在' },
        { status: 404 }
      )
    }

    // 检查是否是商品所有者
    if (product.sellerId === session.user.id) {
      return NextResponse.json(
        { error: '不能购买自己的商品' },
        { status: 400 }
      )
    }

    // 检查商品状态
    if (product.status !== 'AVAILABLE') {
      return NextResponse.json(
        { error: '商品已下架或不可购买' },
        { status: 400 }
      )
    }

    // 处理变体
    let selectedVariant = null
    if (variantId) {
      selectedVariant = product.variants.find(v => v.id === variantId)
      if (!selectedVariant) {
        return NextResponse.json(
          { error: '商品变体不存在' },
          { status: 404 }
        )
      }

      if (selectedVariant.status !== 'AVAILABLE') {
        return NextResponse.json(
          { error: '选择的商品变体已下架' },
          { status: 400 }
        )
      }

      if (selectedVariant.stock < quantity) {
        return NextResponse.json(
          { error: '商品变体库存不足' },
          { status: 400 }
        )
      }
    } else {
      if (product.stock < quantity) {
        return NextResponse.json(
          { error: '商品库存不足' },
          { status: 400 }
        )
      }
    }

    // 计算价格
    const itemPrice = selectedVariant ? selectedVariant.price : product.price
    const productPrice = itemPrice * quantity
    const shippingFee = 0 // 暂时设为0，后续在信息收集页面计算
    const platformFee = 0 // 暂时设为0，后续在信息收集页面计算
    const totalAmount = productPrice + shippingFee + platformFee

    // 生成订单号
    const orderNumber = generateOrderNumber()

    // 创建草稿订单
    const order = await prisma.order.create({
      data: {
        orderNumber,
        productId,
        buyerId: session.user.id,
        sellerId: product.sellerId,
        status: 'DRAFT', // 草稿状态
        totalAmount,
        productPrice,
        shippingFee,
        platformFee,
        useEscrow,
        metadata: {
          quantity,
          variantId: selectedVariant?.id,
          itemPrice,
          createdFrom: 'product-detail'
        }
      }
    })

    // 创建订单项目
    await prisma.orderItem.create({
      data: {
        orderId: order.id,
        productId,
        variantId: selectedVariant?.id,
        quantity,
        unitPrice: itemPrice,
        totalPrice: productPrice
      }
    })

    console.log('✅ 草稿订单创建成功:', order.id)

    return NextResponse.json({
      success: true,
      orderId: order.id,
      orderNumber: order.orderNumber
    })

  } catch (error) {
    console.error('❌ 创建草稿订单失败:', error)
    return NextResponse.json(
      { error: '创建订单失败，请稍后重试' },
      { status: 500 }
    )
  }
}
