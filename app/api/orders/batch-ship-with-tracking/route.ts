import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth/next'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// 批量发货（带运单号）
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { 
      orderIds, 
      shippingCompany, 
      trackingNumbers, 
      estimatedDelivery, 
      shippingNotes 
    } = body

    if (!orderIds || !Array.isArray(orderIds) || orderIds.length === 0) {
      return NextResponse.json(
        { error: '请选择要发货的订单' },
        { status: 400 }
      )
    }

    if (!shippingCompany) {
      return NextResponse.json(
        { error: '请选择快递公司' },
        { status: 400 }
      )
    }

    if (!trackingNumbers || typeof trackingNumbers !== 'object') {
      return NextResponse.json(
        { error: '请提供运单号信息' },
        { status: 400 }
      )
    }

    // 获取订单信息并验证权限
    const orders = await prisma.order.findMany({
      where: {
        id: { in: orderIds },
        sellerId: session.user.id
      },
      select: {
        id: true,
        status: true,
        orderNumber: true
      }
    })

    if (orders.length !== orderIds.length) {
      return NextResponse.json(
        { error: '部分订单不存在或无权限操作' },
        { status: 403 }
      )
    }

    // 检查订单状态
    const invalidOrders = orders.filter(order => order.status !== 'PAID')
    if (invalidOrders.length > 0) {
      return NextResponse.json(
        { error: `订单 ${invalidOrders.map(o => o.orderNumber).join(', ')} 状态不允许发货` },
        { status: 400 }
      )
    }

    // 验证运单号
    const missingTrackingNumbers = orderIds.filter(orderId => !trackingNumbers[orderId]?.trim())
    if (missingTrackingNumbers.length > 0) {
      return NextResponse.json(
        { error: '部分订单缺少运单号' },
        { status: 400 }
      )
    }

    // 检查运单号是否重复
    const trackingNumberValues = Object.values(trackingNumbers).map(tn => tn.trim())
    const uniqueTrackingNumbers = new Set(trackingNumberValues)
    if (uniqueTrackingNumbers.size !== trackingNumberValues.length) {
      return NextResponse.json(
        { error: '运单号不能重复' },
        { status: 400 }
      )
    }

    // 检查运单号是否已被其他订单使用
    const existingOrders = await prisma.order.findMany({
      where: {
        trackingNumber: { in: trackingNumberValues },
        status: { in: ['SHIPPED', 'DELIVERED', 'COMPLETED'] }
      },
      select: {
        trackingNumber: true,
        orderNumber: true
      }
    })

    if (existingOrders.length > 0) {
      const duplicateNumbers = existingOrders.map(o => `${o.trackingNumber} (订单: ${o.orderNumber})`).join(', ')
      return NextResponse.json(
        { error: `以下运单号已被使用: ${duplicateNumbers}` },
        { status: 400 }
      )
    }

    const shippedAt = new Date()
    const results = []

    // 逐个更新订单
    for (const orderId of orderIds) {
      const trackingNumber = trackingNumbers[orderId].trim()
      
      try {
        // 更新订单
        await prisma.order.update({
          where: { id: orderId },
          data: {
            status: 'SHIPPED',
            shippedAt,
            trackingNumber,
            shippingCompany,
            estimatedDelivery: estimatedDelivery ? new Date(estimatedDelivery) : null,
            shippingNotes: shippingNotes || null
          }
        })

        // 创建操作日志
        await prisma.orderLog.create({
          data: {
            orderId,
            operatorId: session.user.id,
            action: 'BATCH_SHIP_ORDER',
            description: `批量发货，快递公司：${shippingCompany}，运单号：${trackingNumber}`
          }
        })

        results.push({
          orderId,
          success: true,
          trackingNumber
        })

      } catch (error) {
        console.error(`更新订单 ${orderId} 失败:`, error)
        results.push({
          orderId,
          success: false,
          error: '更新失败'
        })
      }
    }

    const successCount = results.filter(r => r.success).length
    const failureCount = results.filter(r => !r.success).length

    // TODO: 发送发货通知给买家
    // for (const result of results.filter(r => r.success)) {
    //   await sendShippingNotification(result.orderId)
    // }

    return NextResponse.json({
      success: true,
      message: `批量发货完成，成功：${successCount}个，失败：${failureCount}个`,
      results,
      successCount,
      failureCount
    })

  } catch (error) {
    console.error('批量发货失败:', error)
    return NextResponse.json(
      { error: '批量发货失败' },
      { status: 500 }
    )
  }
}
