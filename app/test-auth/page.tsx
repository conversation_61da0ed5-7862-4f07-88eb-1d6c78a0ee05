'use client'

import { useSession } from 'next-auth/react'
import { useState, useEffect } from 'react'

// 禁用静态生成
export const dynamic = 'force-dynamic'

export default function TestAuthPage() {
  const { data: session, status } = useSession()
  const [apiTestResult, setApiTestResult] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [cookies, setCookies] = useState<string>('')
  const [debugInfo, setDebugInfo] = useState<{
    hostname: string
    port: string
    protocol: string
    userAgent: string
  } | null>(null)

  useEffect(() => {
    // 只在客户端获取 cookies 和调试信息
    if (typeof window !== 'undefined') {
      setCookies(document.cookie || '无 cookies')
      setDebugInfo({
        hostname: window.location.hostname,
        port: window.location.port,
        protocol: window.location.protocol,
        userAgent: navigator.userAgent
      })
    }
  }, [])

  const testUserStatusAPI = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/user/status', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
      })

      const data = await response.json()
      setApiTestResult({
        status: response.status,
        ok: response.ok,
        data: data
      })
    } catch (error) {
      setApiTestResult({
        error: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testMediatorAutoAssign = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/mediator/auto-assign', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          orderAmount: 999
        })
      })

      const data = await response.json()
      setApiTestResult({
        api: 'mediator/auto-assign',
        status: response.status,
        ok: response.ok,
        data: data
      })
    } catch (error) {
      setApiTestResult({
        api: 'mediator/auto-assign',
        error: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  const testFullPurchaseFlow = async () => {
    setLoading(true)
    try {
      // 1. 测试订单创建
      const orderResponse = await fetch('/api/orders', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({
          productId: 'cmdr1z4vv00078oqqm2k4gq7v',
          quantity: 1,
          shippingAddress: {
            name: '测试用户',
            phone: '13800138000',
            province: '广东省',
            city: '深圳市',
            district: '南山区',
            detail: '测试详细地址'
          }
        })
      })

      const orderData = await orderResponse.json()

      if (orderResponse.ok) {
        // 2. 测试中间人分配
        const mediatorResponse = await fetch('/api/mediator/auto-assign', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          credentials: 'include',
          body: JSON.stringify({
            orderAmount: 999
          })
        })

        const mediatorData = await mediatorResponse.json()

        if (mediatorResponse.ok && mediatorData.success) {
          // 3. 测试托管订单创建
          const escrowResponse = await fetch('/api/escrow/create', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              orderId: orderData.id,
              mediatorId: mediatorData.data.mediator.id,
              escrowAmount: 999,
              escrowFee: 24.975
            })
          })

          const escrowData = await escrowResponse.json()

          setApiTestResult({
            api: 'full-purchase-flow',
            steps: {
              order: { status: orderResponse.status, data: orderData },
              mediator: { status: mediatorResponse.status, data: mediatorData },
              escrow: { status: escrowResponse.status, data: escrowData }
            }
          })
        } else {
          setApiTestResult({
            api: 'full-purchase-flow',
            error: '中间人分配失败',
            mediatorError: mediatorData
          })
        }
      } else {
        setApiTestResult({
          api: 'full-purchase-flow',
          error: '订单创建失败',
          orderError: orderData
        })
      }
    } catch (error) {
      setApiTestResult({
        api: 'full-purchase-flow',
        error: error.message
      })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">认证测试页面</h1>
      
      <div className="space-y-6">
        {/* Session 信息 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Session 状态</h2>
          <div className="space-y-2">
            <p><strong>状态:</strong> {status}</p>
            {session ? (
              <div>
                <p><strong>用户ID:</strong> {session.user?.id}</p>
                <p><strong>邮箱:</strong> {session.user?.email}</p>
                <p><strong>姓名:</strong> {session.user?.name}</p>
                <p><strong>角色:</strong> {session.user?.role}</p>
                <p><strong>是否中间人:</strong> {session.user?.isMediator ? '是' : '否'}</p>
              </div>
            ) : (
              <p>未登录</p>
            )}
          </div>
        </div>

        {/* API 测试 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">API 测试</h2>
          <div className="space-y-4">
            <div className="flex space-x-4">
              <button
                onClick={testUserStatusAPI}
                disabled={loading}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {loading ? '测试中...' : '测试用户状态API'}
              </button>
              
              <button
                onClick={testMediatorAutoAssign}
                disabled={loading}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {loading ? '测试中...' : '测试中间人分配API'}
              </button>

              <button
                onClick={testFullPurchaseFlow}
                disabled={loading}
                className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded disabled:opacity-50"
              >
                {loading ? '测试中...' : '测试完整购买流程'}
              </button>
            </div>

            {apiTestResult && (
              <div className="mt-4 p-4 bg-gray-100 rounded">
                <h3 className="font-semibold mb-2">API 测试结果:</h3>
                <pre className="text-sm overflow-auto">
                  {JSON.stringify(apiTestResult, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>

        {/* Cookie 信息 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">Cookie 信息</h2>
          <div className="text-sm">
            <p><strong>所有 Cookies:</strong></p>
            <pre className="mt-2 p-2 bg-gray-100 rounded overflow-auto">
              {cookies}
            </pre>
          </div>
        </div>

        {/* 调试信息 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-lg font-semibold mb-4">调试信息</h2>
          <div className="space-y-2 text-sm">
            {debugInfo ? (
              <>
                <p><strong>当前域名:</strong> {debugInfo.hostname}</p>
                <p><strong>当前端口:</strong> {debugInfo.port}</p>
                <p><strong>协议:</strong> {debugInfo.protocol}</p>
                <p><strong>User Agent:</strong> {debugInfo.userAgent}</p>
              </>
            ) : (
              <p>正在加载调试信息...</p>
            )}
          </div>
        </div>

        {/* 解决方案建议 */}
        <div className="bg-yellow-50 p-6 rounded-lg border border-yellow-200">
          <h2 className="text-lg font-semibold mb-4 text-yellow-800">解决方案建议</h2>
          <div className="space-y-2 text-sm text-yellow-700">
            <p><strong>如果 Session 显示未登录:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>尝试重新登录</li>
              <li>清除浏览器缓存和 cookies</li>
              <li>检查是否在隐私模式下浏览</li>
            </ul>
            
            <p className="mt-4"><strong>如果 API 返回 401 错误:</strong></p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>检查 NextAuth 配置</li>
              <li>确认 cookies 设置正确</li>
              <li>检查服务端 session 处理</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  )
}
